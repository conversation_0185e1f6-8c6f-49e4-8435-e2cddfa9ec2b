using FairyGUI;
using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 折扣礼品弹窗面板
/// </summary>
public class DiscountGiftPanel : Panel
{
    public static int CloseType_Reward = 1;

    private static ItemVo[] rewards;
    private GList listReward;
    private ItemVo[] currentRewards;

    public DiscountGiftPanel()
    {
        packName = "Battle";
        compName = "DiscountGiftPanel";
        modal = true;
    }

    protected override void DoInitialize()
    {
        var txtDesc = contentPane.GetChild("txtDesc").asTextField;
        var rewardCount = ConfigSetting.discountGiftRewardCount;
        txtDesc.text = LangUtil.GetText("txtRandomItems", rewardCount);

        // 获取奖励列表组件
        listReward = contentPane.GetChild("listReward").asList;
        listReward.itemRenderer = UpdateRewardItem;

        // 获取当前关卡的奖励
        currentRewards = GetCurrentLevelRewards();

        // 设置列表数据
        listReward.data = currentRewards;
        listReward.numItems = currentRewards.Length;
    }


    private void UpdateRewardItem(int index, GObject item)
    {
        var icon = item.asCom.GetChild("icon").asLoader;
        var lblItemName = item.asCom.GetChild("lblItemName").asTextField;

        var reward = currentRewards[index];
        var itemInfo = ConfigItem.GetData(reward.itemId);

        icon.url = itemInfo.IconUrl;
        lblItemName.text = $"{itemInfo.name}+{reward.count}";
    }

    /// <summary>
    /// 获取当前关卡的折扣礼品奖励
    /// 如果当前关卡还没有生成过奖励，则随机生成并缓存
    /// </summary>
    /// <returns>当前关卡的奖励</returns>
    private ItemVo[] GetCurrentLevelRewards()
    {
        rewards ??= GenerateRandomRewards();
        return rewards;
    }

    public static void ClearRewards()
    {
        rewards = null;
    }

    /// <summary>
    /// 生成随机奖励，支持去重
    /// </summary>
    /// <returns>随机生成的奖励列表</returns>
    private ItemVo[] GenerateRandomRewards()
    {
        var rewardCount = ConfigSetting.discountGiftRewardCount;
        var rewards = new ItemVo[rewardCount];
        var usedItemIds = new HashSet<int>();

        for (int i = 0; i < rewardCount; i++)
        {
            var drop = GetUniqueDropItem(usedItemIds);
            if (drop != null)
            {
                rewards[i] = new ItemVo(drop.itemId, drop.count);
                usedItemIds.Add(drop.itemId);
            }
            else
            {
                // 如果配置有问题或无法获取唯一道具，给默认道具
                rewards[i] = new ItemVo(ItemId.ItemPrompt, 1);
            }
        }

        return rewards;
    }

    /// <summary>
    /// 获取唯一的掉落道具，避免重复
    /// </summary>
    /// <param name="usedItemIds">已使用的道具ID集合</param>
    /// <returns>唯一的掉落道具，如果无法获取则返回null</returns>
    private InfoDropItem GetUniqueDropItem(HashSet<int> usedItemIds)
    {
        const int maxAttempts = 50; // 最大尝试次数，避免无限循环

        for (int attempt = 0; attempt < maxAttempts; attempt++)
        {
            var drop = ConfigDropItem.GetData(DropIds.DiscountGift);
            if (drop != null && !usedItemIds.Contains(drop.itemId))
            {
                return drop;
            }
        }

        // 如果尝试多次仍无法获取唯一道具，返回null
        return null;
    }

    /// <summary>
    /// 获取当前显示的奖励列表
    /// </summary>
    /// <returns>奖励列表</returns>
    public ItemVo[] GetCurrentRewards()
    {
        return currentRewards;
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnFree":
                OnFreeButtonClick();
                break;
            case "btnClose":
                Hide();
                break;
        }
    }

    private void OnFreeButtonClick()
    {
        Hide(CloseType_Reward);
    }
}
